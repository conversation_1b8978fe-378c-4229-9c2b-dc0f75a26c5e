const fs = require('fs');
const path = require('path');

function analyzeSQLFile() {
  const sqlFilePath = path.join(__dirname, '../output/taxrate_dictionary_import.sql');
  const content = fs.readFileSync(sqlFilePath, 'utf8');
  
  // 统计各种SQL语句数量
  const versionInserts = (content.match(/INSERT INTO ecost\.taxrate_dictionary_version/g) || []).length;
  const categoryInserts = (content.match(/INSERT INTO ecost\.taxrate_dictionary_category/g) || []).length;
  const detailInserts = (content.match(/INSERT INTO ecost\.taxrate_dictionary_detail/g) || []).length;
  
  console.log('=== SQL文件统计 ===');
  console.log(`版本表插入语句: ${versionInserts} 条`);
  console.log(`分类表插入语句: ${categoryInserts} 条`);
  console.log(`明细表插入语句: ${detailInserts} 条`);
  console.log(`总计: ${versionInserts + categoryInserts + detailInserts} 条`);
  
  // 提取版本ID
  const versionMatch = content.match(/'([0-9a-f-]+)', '税率字典导入_\d{4}-\d{2}-\d{2}'/);
  if (versionMatch) {
    console.log(`\n版本ID: ${versionMatch[1]}`);
  }
  
  // 分析分类层级
  const categoryMatches = content.match(/full_id', '([^']+)'/g) || [];
  const categories = categoryMatches.map(match => match.match(/'([^']+)'/)[1]);
  const levels = {};
  
  categories.forEach(category => {
    const level = category.split('-').length;
    levels[level] = (levels[level] || 0) + 1;
  });
  
  console.log('\n=== 分类层级统计 ===');
  Object.keys(levels).sort().forEach(level => {
    console.log(`${level}级分类: ${levels[level]} 个`);
  });
  
  // 分析税率分布
  const taxRateMatches = content.match(/tax_rate, execute_date[^']*'([^']+)'/g) || [];
  const taxRates = taxRateMatches.map(match => match.match(/'([^']+)'/)[1]);
  const rateDistribution = {};
  
  taxRates.forEach(rate => {
    rateDistribution[rate] = (rateDistribution[rate] || 0) + 1;
  });
  
  console.log('\n=== 税率分布统计 ===');
  Object.keys(rateDistribution).sort().forEach(rate => {
    console.log(`税率 ${rate}: ${rateDistribution[rate]} 个明细`);
  });
}

analyzeSQLFile();
