-- ecost.taxrate_dictionary_enable_detail definition

-- Drop table

-- DROP TABLE ecost.taxrate_dictionary_enable_detail;

CREATE TABLE ecost.taxrate_dictionary_enable_detail (
	tenant_id text NOT NULL,
	org_id text NOT NULL,
	id text NOT NULL,
	taxrate_dictionary_version_id text NOT NULL,
	taxrate_dictionary_category_id text NOT NULL,
	tax_rate text NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	create_by text DEFAULT 'system'::text NOT NULL,
	update_by text DEFAULT 'system'::text NOT NULL,
	create_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_at timestamp(3) NOT NULL,
	CONSTRAINT taxrate_dictionary_enable_detail_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX taxrate_dictionary_enable_detail_tenant_id_org_id_id_key ON ecost.taxrate_dictionary_enable_detail USING btree (tenant_id, org_id, id);


-- ecost.taxrate_dictionary_version definition

-- Drop table

-- DROP TABLE ecost.taxrate_dictionary_version;

CREATE TABLE ecost.taxrate_dictionary_version (
	tenant_id text NOT NULL,
	org_id text NOT NULL,
	id text NOT NULL,
	"name" text NOT NULL,
	status ecost."EnableStatus" DEFAULT 'NOT_ENABLED'::ecost."EnableStatus" NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	create_by text DEFAULT 'system'::text NOT NULL,
	update_by text DEFAULT 'system'::text NOT NULL,
	create_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_at timestamp(3) NOT NULL,
	CONSTRAINT taxrate_dictionary_version_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX taxrate_dictionary_version_tenant_id_org_id_id_key ON ecost.taxrate_dictionary_version USING btree (tenant_id, org_id, id);


-- ecost.taxrate_dictionary_category definition

-- Drop table

-- DROP TABLE ecost.taxrate_dictionary_category;

CREATE TABLE ecost.taxrate_dictionary_category (
	tenant_id text NOT NULL,
	org_id text NOT NULL,
	id text NOT NULL,
	taxrate_dictionary_version_id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	remark text NULL,
	is_active bool DEFAULT true NOT NULL,
	parent_id text NULL,
	full_id text DEFAULT ''::text NOT NULL,
	full_name text DEFAULT ''::text NOT NULL,
	"level" int4 DEFAULT 1 NOT NULL,
	sort int4 DEFAULT 1 NOT NULL,
	is_leaf bool DEFAULT true NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	create_by text DEFAULT 'system'::text NOT NULL,
	update_by text DEFAULT 'system'::text NOT NULL,
	create_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_at timestamp(3) NOT NULL,
	CONSTRAINT taxrate_dictionary_category_pkey PRIMARY KEY (id),
	CONSTRAINT taxrate_dictionary_category_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES ecost.taxrate_dictionary_category(id) ON DELETE SET NULL ON UPDATE CASCADE,
	CONSTRAINT taxrate_dictionary_category_taxrate_dictionary_version_id_fkey FOREIGN KEY (taxrate_dictionary_version_id) REFERENCES ecost.taxrate_dictionary_version(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX taxrate_dictionary_category_tenant_id_org_id_id_key ON ecost.taxrate_dictionary_category USING btree (tenant_id, org_id, id);


-- ecost.taxrate_dictionary_detail definition

-- Drop table

-- DROP TABLE ecost.taxrate_dictionary_detail;

CREATE TABLE ecost.taxrate_dictionary_detail (
	tenant_id text NOT NULL,
	org_id text NOT NULL,
	id text NOT NULL,
	taxrate_dictionary_version_id text NOT NULL,
	taxrate_dictionary_category_id text NOT NULL,
	code text NOT NULL,
	"name" text NOT NULL,
	"type" ecost."TaxrateType" NOT NULL,
	remark text NULL,
	sort int4 DEFAULT 1 NOT NULL,
	is_active bool DEFAULT true NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	create_by text DEFAULT 'system'::text NOT NULL,
	update_by text DEFAULT 'system'::text NOT NULL,
	create_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_at timestamp(3) NOT NULL,
	tax_rate text NOT NULL,
	execute_date timestamp(3) NULL,
	CONSTRAINT taxrate_dictionary_detail_pkey PRIMARY KEY (id),
	CONSTRAINT taxrate_dictionary_detail_taxrate_dictionary_category_id_fkey FOREIGN KEY (taxrate_dictionary_category_id) REFERENCES ecost.taxrate_dictionary_category(id) ON DELETE RESTRICT ON UPDATE CASCADE,
	CONSTRAINT taxrate_dictionary_detail_taxrate_dictionary_version_id_fkey FOREIGN KEY (taxrate_dictionary_version_id) REFERENCES ecost.taxrate_dictionary_version(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX taxrate_dictionary_detail_tenant_id_org_id_id_key ON ecost.taxrate_dictionary_detail USING btree (tenant_id, org_id, id);


-- ecost.taxrate_dictionary_change_record definition

-- Drop table

-- DROP TABLE ecost.taxrate_dictionary_change_record;

CREATE TABLE ecost.taxrate_dictionary_change_record (
	tenant_id text NOT NULL,
	org_id text NOT NULL,
	id text NOT NULL,
	taxrate_dictionary_detail_id text NOT NULL,
	field_name text NOT NULL,
	old_value text NOT NULL,
	new_value text NOT NULL,
	opreate_user_id text NOT NULL,
	opreate_user_name text NOT NULL,
	is_deleted bool DEFAULT false NOT NULL,
	create_by text DEFAULT 'system'::text NOT NULL,
	update_by text DEFAULT 'system'::text NOT NULL,
	create_at timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	update_at timestamp(3) NOT NULL,
	CONSTRAINT taxrate_dictionary_change_record_pkey PRIMARY KEY (id),
	CONSTRAINT taxrate_dictionary_change_record_taxrate_dictionary_detail_fkey FOREIGN KEY (taxrate_dictionary_detail_id) REFERENCES ecost.taxrate_dictionary_detail(id) ON DELETE RESTRICT ON UPDATE CASCADE
);
CREATE UNIQUE INDEX taxrate_dictionary_change_record_tenant_id_org_id_id_key ON ecost.taxrate_dictionary_change_record USING btree (tenant_id, org_id, id);