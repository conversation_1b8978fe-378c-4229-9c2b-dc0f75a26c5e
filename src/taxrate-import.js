const ExcelJS = require('exceljs');
const { uuidv7 } = require('uuidv7');
const { tenantId, orgId } = require('./constant');
const fs = require('fs');
const path = require('path');

class TaxrateImporter {
  constructor() {
    this.versionId = uuidv7();
    this.categoryMap = new Map(); // 存储分类ID映射
    this.categoryValues = []; // 存储分类VALUES
    this.detailValues = []; // 存储明细VALUES
  }

  // 解析层级码，返回层级数组
  parseHierarchyCode(code) {
    if (!code || typeof code !== 'string') return [];
    return code.split('-').filter(part => part.trim() !== '');
  }

  // 生成版本表SQL
  generateVersionSQL(versionName) {
    const sql = `INSERT INTO ecost.taxrate_dictionary_version (
      tenant_id, org_id, id, name, status, is_deleted,
      create_by, update_by, create_at, update_at
    ) VALUES (
      '${tenantId}', '${orgId}', '${this.versionId}', '${versionName}',
      'NOT_ENABLED', false, 'system', 'system',
      CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    );`;
    return sql;
  }

  // 添加分类VALUES
  addCategoryValue(categoryData) {
    const {
      id, code, name, remark, parentId, fullId, fullName, level, sort, isLeaf
    } = categoryData;

    const value = `('${tenantId}', '${orgId}', '${id}', '${this.versionId}', '${code}',
      '${name}', ${remark ? `'${remark}'` : 'NULL'}, true,
      ${parentId ? `'${parentId}'` : 'NULL'}, '${fullId}', '${fullName}',
      ${level}, ${sort}, ${isLeaf}, false, 'system', 'system',
      CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

    this.categoryValues.push(value);
  }

  // 添加明细VALUES
  addDetailValue(detailData) {
    const {
      id, categoryId, code, name, type, remark, sort, taxRate, executeDate
    } = detailData;

    const value = `('${tenantId}', '${orgId}', '${id}', '${this.versionId}',
      '${categoryId}', '${code}', '${name}', '${type}',
      ${remark ? `'${remark}'` : 'NULL'}, ${sort}, true, false,
      'system', 'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,
      '${taxRate}', ${executeDate ? `'${executeDate}'` : 'NULL'})`;

    this.detailValues.push(value);
  }

  // 生成分类表批量SQL
  generateCategoryBatchSQL() {
    if (this.categoryValues.length === 0) return '';

    const sql = `INSERT INTO ecost.taxrate_dictionary_category (
      tenant_id, org_id, id, taxrate_dictionary_version_id, code, name,
      remark, is_active, parent_id, full_id, full_name, level, sort,
      is_leaf, is_deleted, create_by, update_by, create_at, update_at
    ) VALUES
${this.categoryValues.join(',\n')};`;
    return sql;
  }

  // 生成明细表批量SQL
  generateDetailBatchSQL() {
    if (this.detailValues.length === 0) return '';

    const sql = `INSERT INTO ecost.taxrate_dictionary_detail (
      tenant_id, org_id, id, taxrate_dictionary_version_id,
      taxrate_dictionary_category_id, code, name, type, remark,
      sort, is_active, is_deleted, create_by, update_by,
      create_at, update_at, tax_rate, execute_date
    ) VALUES
${this.detailValues.join(',\n')};`;
    return sql;
  }

  // 处理分类层级结构
  processCategoryHierarchy(hierarchyCode, name, remark = null) {
    const levels = this.parseHierarchyCode(hierarchyCode);
    if (levels.length === 0) return null;

    let currentParentId = null;
    let fullId = '';
    let fullName = '';

    // 逐级处理每个层级
    for (let i = 0; i < levels.length; i++) {
      const code = levels[i];
      const level = i + 1;
      const isLeaf = (i === levels.length - 1);
      
      // 构建完整ID和名称
      fullId = levels.slice(0, i + 1).join('-');
      
      // 检查是否已存在该分类
      const categoryKey = fullId;
      if (!this.categoryMap.has(categoryKey)) {
        const categoryId = uuidv7();
        this.categoryMap.set(categoryKey, categoryId);

        // 对于最后一级，使用传入的name，否则使用code作为name
        const categoryName = isLeaf ? name : code;
        fullName = this.buildFullName(levels.slice(0, i + 1));

        const categoryData = {
          id: categoryId,
          code: code,
          name: categoryName,
          remark: isLeaf ? remark : null,
          parentId: currentParentId,
          fullId: fullId,
          fullName: fullName,
          level: level,
          sort: 1,
          isLeaf: isLeaf
        };

        this.addCategoryValue(categoryData);
      }

      currentParentId = this.categoryMap.get(categoryKey);
    }

    return this.categoryMap.get(fullId);
  }

  // 构建完整名称
  buildFullName(levels) {
    // 这里可以根据实际需求调整名称构建逻辑
    return levels.join('/');
  }

  // 处理Excel文件
  async processExcelFile(filePath) {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);

    console.log('工作表数量:', workbook.worksheets.length);
    const worksheet = workbook.worksheets[0]; // 获取第一个工作表
    if (!worksheet) {
      throw new Error('Excel文件中没有找到工作表');
    }

    console.log('工作表名称:', worksheet.name);
    console.log('行数:', worksheet.rowCount);

    // 生成版本SQL
    const versionName = `税率字典导入_${new Date().toISOString().split('T')[0]}`;
    const versionSQL = this.generateVersionSQL(versionName);

    // 获取表头
    const headerRow = worksheet.getRow(1);
    const headers = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });

    console.log('表头:', headers);

    // 找到关键列的索引 - 根据实际Excel结构调整
    const hierarchyCodeIndex = 1; // 层级码
    const codeIndex = 2; // 编码
    const nameIndex = 3; // 名称
    const invoiceTypeIndex = 4; // 发票类型
    const taxRateIndex = 5; // 税率
    const remarkIndex = 6; // 备注

    console.log(`列索引 - 层级码: ${hierarchyCodeIndex}, 编码: ${codeIndex}, 名称: ${nameIndex}, 发票类型: ${invoiceTypeIndex}, 税率: ${taxRateIndex}, 备注: ${remarkIndex}`);

    // 处理数据行
    let detailSort = 1;
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      const hierarchyCode = this.getCellValue(row, hierarchyCodeIndex);
      const code = this.getCellValue(row, codeIndex);
      const name = this.getCellValue(row, nameIndex);
      const invoiceType = this.getCellValue(row, invoiceTypeIndex);
      const taxRate = this.getCellValue(row, taxRateIndex);
      const remark = this.getCellValue(row, remarkIndex);

      if (!hierarchyCode || !name) continue; // 跳过无效行

      console.log(`处理行${rowNumber}: 层级码=${hierarchyCode}, 名称=${name}, 税率=${taxRate}`);

      // 处理分类
      const categoryId = this.processCategoryHierarchy(hierarchyCode, name, remark);

      // 如果有税率，则创建明细记录
      if (taxRate !== null && taxRate !== undefined && taxRate.toString().trim() !== '') {
        const detailData = {
          id: uuidv7(),
          categoryId: categoryId,
          code: code || hierarchyCode, // 如果没有编码，使用层级码
          name: name,
          type: 'PERCENTAGE', // 假设默认为百分比类型
          remark: remark,
          sort: detailSort++,
          taxRate: taxRate.toString(),
          executeDate: null
        };

        this.addDetailValue(detailData);
      }
    }

    // 生成最终的SQL语句
    const sqlStatements = [];
    sqlStatements.push(versionSQL);

    const categorySQL = this.generateCategoryBatchSQL();
    if (categorySQL) sqlStatements.push(categorySQL);

    const detailSQL = this.generateDetailBatchSQL();
    if (detailSQL) sqlStatements.push(detailSQL);

    return sqlStatements;
  }

  // 查找列索引
  findColumnIndex(headers, possibleNames) {
    for (let i = 1; i < headers.length; i++) {
      const header = headers[i];
      if (header && possibleNames.some(name => 
        header.toString().toLowerCase().includes(name.toLowerCase())
      )) {
        return i;
      }
    }
    return null;
  }

  // 获取单元格值
  getCellValue(row, columnIndex) {
    if (!columnIndex) return null;
    const cell = row.getCell(columnIndex);
    return cell.value ? cell.value.toString().trim() : null;
  }

  // 输出SQL到文件
  async outputSQL(outputPath, sqlStatements) {
    const sqlContent = sqlStatements.join('\n\n');
    await fs.promises.writeFile(outputPath, sqlContent, 'utf8');
    console.log(`SQL文件已生成: ${outputPath}`);
    return sqlContent;
  }
}

// 主函数
async function main() {
  try {
    const importer = new TaxrateImporter();
    const excelFilePath = path.join(__dirname, '../file/税率字典发布.xlsx');
    const outputPath = path.join(__dirname, '../output/taxrate_dictionary_import.sql');

    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    console.log('开始处理Excel文件...');
    const sqlStatements = await importer.processExcelFile(excelFilePath);

    console.log('生成SQL语句...');
    await importer.outputSQL(outputPath, sqlStatements);

    console.log(`处理完成！共生成 ${sqlStatements.length} 条SQL语句`);
    console.log(`版本ID: ${importer.versionId}`);
    
  } catch (error) {
    console.error('处理失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = TaxrateImporter;
