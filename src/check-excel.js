const ExcelJS = require('exceljs');
const path = require('path');

async function checkExcelFile() {
  try {
    const filePath = path.join(__dirname, '../file/税率字典发布.xlsx');
    console.log('检查文件:', filePath);
    
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    
    console.log('工作表数量:', workbook.worksheets.length);
    
    workbook.worksheets.forEach((worksheet, index) => {
      console.log(`工作表 ${index + 1}:`);
      console.log('  名称:', worksheet.name);
      console.log('  行数:', worksheet.rowCount);
      console.log('  列数:', worksheet.columnCount);
      
      // 显示前几行数据
      console.log('  前5行数据:');
      for (let i = 1; i <= Math.min(5, worksheet.rowCount); i++) {
        const row = worksheet.getRow(i);
        const rowData = [];
        row.eachCell((cell, colNumber) => {
          rowData[colNumber] = cell.value;
        });
        console.log(`    行${i}:`, rowData.slice(1, 10)); // 显示前10列
      }
    });
    
  } catch (error) {
    console.error('检查Excel文件失败:', error.message);
  }
}

checkExcelFile();
