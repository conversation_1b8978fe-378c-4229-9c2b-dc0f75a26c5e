hoistPattern:
  - '*'
hoistedDependencies:
  '@fast-csv/format@4.3.5':
    '@fast-csv/format': private
  '@fast-csv/parse@4.3.6':
    '@fast-csv/parse': private
  '@types/node@14.18.63':
    '@types/node': private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  async@3.2.6:
    async: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  big-integer@1.6.52:
    big-integer: private
  binary@0.3.0:
    binary: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  brace-expansion@2.0.2:
    brace-expansion: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-indexof-polyfill@1.0.2:
    buffer-indexof-polyfill: private
  buffer@5.7.1:
    buffer: private
  buffers@0.1.1:
    buffers: private
  chainsaw@0.1.0:
    chainsaw: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  dayjs@1.11.13:
    dayjs: private
  duplexer2@0.1.4:
    duplexer2: private
  end-of-stream@1.4.5:
    end-of-stream: private
  fast-csv@4.3.6:
    fast-csv: private
  fs-constants@1.0.0:
    fs-constants: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fstream@1.0.12:
    fstream: private
  glob@7.2.3:
    glob: private
  graceful-fs@4.2.11:
    graceful-fs: private
  ieee754@1.2.1:
    ieee754: private
  immediate@3.0.6:
    immediate: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  isarray@1.0.0:
    isarray: private
  jszip@3.10.1:
    jszip: private
  lazystream@1.0.1:
    lazystream: private
  lie@3.3.0:
    lie: private
  listenercount@1.0.1:
    listenercount: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  minimatch@5.1.6:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  normalize-path@3.0.0:
    normalize-path: private
  once@1.4.0:
    once: private
  pako@1.0.11:
    pako: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  rimraf@2.7.1:
    rimraf: private
  safe-buffer@5.1.2:
    safe-buffer: private
  saxes@5.0.1:
    saxes: private
  setimmediate@1.0.5:
    setimmediate: private
  string_decoder@1.3.0:
    string_decoder: private
  tar-stream@2.2.0:
    tar-stream: private
  tmp@0.2.3:
    tmp: private
  traverse@0.3.9:
    traverse: private
  unzipper@0.10.14:
    unzipper: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  wrappy@1.0.2:
    wrappy: private
  xmlchars@2.2.0:
    xmlchars: private
  zip-stream@4.1.1:
    zip-stream: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 27 Jun 2025 02:07:36 GMT
publicHoistPattern: []
registries:
  '@ewing': https://npm.ewing.world/
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
