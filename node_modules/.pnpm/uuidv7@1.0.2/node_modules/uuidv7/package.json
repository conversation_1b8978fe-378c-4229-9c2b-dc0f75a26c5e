{"name": "uuidv7", "version": "1.0.2", "description": "A JavaScript implementation of UUID version 7", "type": "module", "main": "dist/index.js", "types": "./dist/index.d.ts", "exports": {"require": "./dist/index.cjs", "default": "./dist/index.js"}, "bin": {"uuidv7": "cli.js"}, "files": ["CHANGELOG.md", "dist"], "sideEffects": false, "scripts": {"build": "tsc", "doc": "typedoc ./src/index.ts --gitRevision \"v$npm_package_version\"", "prebuild": "rm -rf ./dist", "predoc": "rm -rf ./docs", "prepare": "npm run build && npm run doc && npm run test", "test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/LiosK/uuidv7.git"}, "keywords": ["uuid", "uuidv7"], "author": "LiosK <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/LiosK/uuidv7/issues"}, "homepage": "https://github.com/LiosK/uuidv7#readme", "devDependencies": {"mocha": "^10.7.3", "typedoc": "^0.26.6", "typescript": "^5.5.4"}}